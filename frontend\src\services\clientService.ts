import { API_CONFIG, API_ENDPOINTS } from '@/config/api';
import { 
  Client, 
  ClientListResponse, 
  ClientFilters, 
  IndividualClientForm, 
  CompanyClientForm,
  ClientStats 
} from '@/types/client';

class ClientService {
  private getAuthHeaders() {
    const token = localStorage.getItem('twins-bank-token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  // Listar clientes com filtros e paginação
  async getClients(filters: ClientFilters): Promise<ClientListResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.LIST}?${queryParams}`;
    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Erro ao carregar clientes: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  }

  // Obter cliente por ID
  async getClientById(id: string): Promise<Client> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.GET(id)}`;
    const response = await fetch(url, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Erro ao carregar cliente: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.client;
  }

  // Criar cliente individual
  async createIndividualClient(clientData: IndividualClientForm): Promise<Client> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.CREATE_INDIVIDUAL}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(clientData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `Erro ao criar cliente: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.client;
  }

  // Criar cliente empresa
  async createCompanyClient(clientData: CompanyClientForm): Promise<Client> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.CREATE_COMPANY}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(clientData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `Erro ao criar cliente: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.client;
  }

  // Atualizar cliente
  async updateClient(id: string, clientData: Partial<Client>): Promise<Client> {
    const url = `${API_CONFIG.baseURL}${API_ENDPOINTS.CLIENTS.UPDATE(id)}`;
    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(clientData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || `Erro ao atualizar cliente: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.client;
  }

  // Obter estatísticas de clientes
  async getClientStats(): Promise<ClientStats> {
    // Usar limite máximo permitido pelo backend (100) e calcular estatísticas baseadas na primeira página
    const allClients = await this.getClients({ page: 1, limit: 100 });

    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Para estatísticas mais precisas, usamos os dados de paginação para total_clients
    // e calculamos as outras métricas baseadas na amostra disponível
    const stats: ClientStats = {
      total_clients: allClients.pagination.total_records, // Valor real do total
      active_clients: allClients.clients.filter(c => c.status === 'active').length,
      new_this_month: allClients.clients.filter(c =>
        new Date(c.created_at) >= thisMonth
      ).length,
      individual_clients: allClients.clients.filter(c => c.client_type === 'individual').length,
      company_clients: allClients.clients.filter(c => c.client_type === 'company').length
    };

    return stats;
  }

  // Exportar clientes para CSV
  async exportClients(filters: ClientFilters): Promise<void> {
    const clients = await this.getClients({ ...filters, limit: 10000 });
    
    const headers = [
      'ID',
      'Tipo',
      'Nome/Empresa',
      'Documento',
      'Número Documento',
      'NIF',
      'Nacionalidade',
      'Profissão',
      'Rendimento Mensal',
      'Status',
      'Balcão',
      'Data Criação',
      'Telefone',
      'Email',
      'Endereço'
    ];

    const csvContent = [
      headers.join(','),
      ...clients.clients.map(client => [
        client.id,
        client.client_type === 'individual' ? 'Individual' : 'Empresa',
        `"${client.full_name || client.company_name || ''}"`,
        client.document_type,
        client.document_number,
        client.nif || '',
        `"${client.nationality}"`,
        `"${client.profession || ''}"`,
        client.monthly_income || '',
        client.status,
        `"${client.branch_name}"`,
        new Date(client.created_at).toLocaleDateString('pt-PT'),
        client.primary_contact?.phone || '',
        client.primary_contact?.email || '',
        client.primary_address ? 
          `"${client.primary_address.street}, ${client.primary_address.municipality}, ${client.primary_address.province}"` : ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `clientes_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}

export const clientService = new ClientService();
