
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Shield, Plus, Eye, FileText, Users, TrendingUp, AlertCircle } from 'lucide-react';

const Seguros = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [novaApolice, setNovaApolice] = useState({
    cliente: '',
    tipoSeguro: '',
    valorCobertura: '',
    premio: '',
    vigencia: ''
  });

  // Dados de exemplo para apólices
  const apolices = [
    {
      id: 'SEG001',
      cliente: 'João Manuel Silva',
      tipoSeguro: 'Vida',
      valorCobertura: 5000000,
      premio: 45000,
      vigencia: '2025-12-31',
      status: 'ativa'
    },
    {
      id: 'SEG002',
      cliente: 'Maria Fernanda Costa',
      tipoSeguro: 'Automóvel',
      valorCobertura: 2500000,
      premio: 180000,
      vigencia: '2025-08-15',
      status: 'ativa'
    },
    {
      id: 'SEG003',
      cliente: 'António José Pereira',
      tipoSeguro: 'Habitação',
      valorCobertura: 15000000,
      premio: 120000,
      vigencia: '2025-06-30',
      status: 'vencida'
    }
  ];

  const formatarValor = (valor: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0
    }).format(valor);
  };

  const handleAdicionarApolice = () => {
    // Lógica para adicionar nova apólice
    console.log('Nova apólice:', novaApolice);
    setIsModalOpen(false);
    setNovaApolice({
      cliente: '',
      tipoSeguro: '',
      valorCobertura: '',
      premio: '',
      vigencia: ''
    });
  };

  return (
    <div className="space-y-6 content-container">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Shield className="h-8 w-8" />
            Gestão de Seguros
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Administração de apólices e produtos de seguro</p>
        </div>

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button size="lg" className="bg-twins-primary hover:bg-twins-secondary text-white shadow-lg">
              <Plus className="h-5 w-5 mr-2" />
              Nova Apólice
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Nova Apólice de Seguro
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="cliente">Cliente</Label>
                <Input
                  id="cliente"
                  value={novaApolice.cliente}
                  onChange={(e) => setNovaApolice({...novaApolice, cliente: e.target.value})}
                  placeholder="Nome do cliente"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tipoSeguro">Tipo de Seguro</Label>
                <Select value={novaApolice.tipoSeguro} onValueChange={(value) => setNovaApolice({...novaApolice, tipoSeguro: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="vida">Seguro de Vida</SelectItem>
                    <SelectItem value="automovel">Seguro Automóvel</SelectItem>
                    <SelectItem value="habitacao">Seguro Habitação</SelectItem>
                    <SelectItem value="saude">Seguro de Saúde</SelectItem>
                    <SelectItem value="viagem">Seguro de Viagem</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="valorCobertura">Valor de Cobertura (Kz)</Label>
                <Input
                  id="valorCobertura"
                  type="number"
                  value={novaApolice.valorCobertura}
                  onChange={(e) => setNovaApolice({...novaApolice, valorCobertura: e.target.value})}
                  placeholder="0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="premio">Prémio Anual (Kz)</Label>
                <Input
                  id="premio"
                  type="number"
                  value={novaApolice.premio}
                  onChange={(e) => setNovaApolice({...novaApolice, premio: e.target.value})}
                  placeholder="0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="vigencia">Data de Vigência</Label>
                <Input
                  id="vigencia"
                  type="date"
                  value={novaApolice.vigencia}
                  onChange={(e) => setNovaApolice({...novaApolice, vigencia: e.target.value})}
                />
              </div>

              <Button onClick={handleAdicionarApolice} className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Criar Apólice
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Cards de Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Apólices Ativas</CardTitle>
            <Shield className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {apolices.filter(a => a.status === 'ativa').length}
            </div>
            <p className="text-xs text-muted-foreground">
              +2 este mês
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Apólices Vencidas</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {apolices.filter(a => a.status === 'vencida').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Necessitam renovação
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valor Total Segurado</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatarValor(apolices.reduce((total, a) => total + a.valorCobertura, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Cobertura total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Prémios Mensais</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {formatarValor(apolices.reduce((total, a) => total + a.premio, 0) / 12)}
            </div>
            <p className="text-xs text-muted-foreground">
              Receita média mensal
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Apólices */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Apólices de Seguro ({apolices.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Apólice</TableHead>
                <TableHead>Cliente</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Cobertura</TableHead>
                <TableHead>Prémio Anual</TableHead>
                <TableHead>Vigência</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apolices.map((apolice) => (
                <TableRow key={apolice.id}>
                  <TableCell className="font-medium">{apolice.id}</TableCell>
                  <TableCell>{apolice.cliente}</TableCell>
                  <TableCell>{apolice.tipoSeguro}</TableCell>
                  <TableCell>{formatarValor(apolice.valorCobertura)}</TableCell>
                  <TableCell>{formatarValor(apolice.premio)}</TableCell>
                  <TableCell>{new Date(apolice.vigencia).toLocaleDateString('pt-AO')}</TableCell>
                  <TableCell>
                    <Badge variant={apolice.status === 'ativa' ? 'default' : 'destructive'}>
                      {apolice.status === 'ativa' ? 'Ativa' : 'Vencida'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <FileText className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default Seguros;
