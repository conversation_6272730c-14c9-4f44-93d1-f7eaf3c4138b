import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Users, Search, Edit, Trash2, UserCheck, UserX, Eye, Loader2, RefreshCw } from 'lucide-react';
import ActionMenu from '@/components/ui/ActionMenu';
import { userService, User, UserFilters, UserError } from '@/services/userService';
import { branchService, Branch } from '@/services/branchService';
import UserViewModal from '@/components/modals/UserViewModal';
import UserEditModal from '@/components/modals/UserEditModal';

const ListarUsuario = () => {
  const { toast } = useToast();

  // Estados principais
  const [users, setUsers] = useState<User[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Estados de filtros
  const [filters, setFilters] = useState<UserFilters>({
    page: 1,
    limit: 10,
    search: '',
    role: '',
    branch: undefined,
    active: undefined
  });

  // Estados de paginação
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Estados dos modais
  const [viewModalUser, setViewModalUser] = useState<User | null>(null);
  const [editModalUser, setEditModalUser] = useState<User | null>(null);

  // Carregar dados iniciais
  useEffect(() => {
    loadInitialData();
  }, []);

  // Carregar usuários quando filtros mudarem
  useEffect(() => {
    loadUsers();
  }, [filters]);

  const loadInitialData = async () => {
    try {
      const branchesData = await branchService.getActiveBranches();
      setBranches(branchesData);
    } catch (error) {
      console.error('Erro ao carregar balcões:', error);
      toast({
        title: "Erro ao carregar dados",
        description: "Não foi possível carregar a lista de balcões",
        variant: "destructive"
      });
    }
  };

  const loadUsers = async () => {
    setIsLoading(true);
    try {
      const response = await userService.listUsers(filters);
      setUsers(response.users);
      setTotalUsers(response.pagination.total_records || response.pagination.total || 0);
      setTotalPages(response.pagination.total_pages || response.pagination.totalPages || 1);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      toast({
        title: "Erro ao carregar usuários",
        description: error instanceof UserError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para atualizar filtros
  const updateFilter = (key: keyof UserFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset page when other filters change
    }));
  };

  // Função para refresh manual
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadUsers();
    setIsRefreshing(false);
    toast({
      title: "Lista atualizada",
      description: "A lista de usuários foi atualizada com sucesso",
    });
  };

  // Função para visualizar usuário
  const handleViewUser = (user: User) => {
    setViewModalUser(user);
  };

  // Função para editar usuário
  const handleEditUser = (user: User) => {
    setEditModalUser(user);
  };

  // Função para ativar/desativar usuário
  const handleToggleUserStatus = async (user: User) => {
    try {
      await userService.toggleUserStatus(user.id, !user.is_active);
      toast({
        title: "Status alterado",
        description: `Usuário ${user.full_name} foi ${!user.is_active ? 'ativado' : 'desativado'}`,
      });
      await loadUsers();
    } catch (error) {
      console.error('Erro ao alterar status:', error);
      toast({
        title: "Erro ao alterar status",
        description: error instanceof UserError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    }
  };

  // Função para deletar usuário
  const handleDeleteUser = async (user: User) => {
    if (!confirm(`Tem certeza que deseja remover o usuário "${user.full_name}"?`)) {
      return;
    }

    try {
      await userService.deleteUser(user.id);
      toast({
        title: "Usuário removido",
        description: `Usuário ${user.full_name} foi removido com sucesso`,
      });
      await loadUsers();
    } catch (error) {
      console.error('Erro ao remover usuário:', error);
      toast({
        title: "Erro ao remover usuário",
        description: error instanceof UserError ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    }
  };

  // Função para obter badge de status
  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? "default" : "secondary"}>
        {isActive ? "Ativo" : "Inativo"}
      </Badge>
    );
  };

  // Função para formatar data
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nunca';
    return new Date(dateString).toLocaleString('pt-AO');
  };

  // Opções de filtro
  const roleOptions = [
    { value: 'all', label: 'Todos os Perfis' },
    { value: 'admin', label: 'Administrador' },
    { value: 'gerente', label: 'Gerente' },
    { value: 'caixa', label: 'Operador de Caixa' },
    { value: 'tesoureiro', label: 'Tesoureiro' }
  ];

  const statusOptions = [
    { value: 'all', label: 'Todos os Status' },
    { value: 'true', label: 'Ativo' },
    { value: 'false', label: 'Inativo' }
  ];

  return (
    <div className="space-y-6 content-container">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Users className="h-8 w-8" />
            Listar Usuários
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Gerir usuários do sistema</p>
        </div>
        <Button onClick={handleRefresh} disabled={isRefreshing} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros de Busca</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Busca */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Buscar</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Nome ou email..."
                  value={filters.search || ''}
                  onChange={(e) => updateFilter('search', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Perfil */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Perfil</label>
              <Select
                value={filters.role || 'all'}
                onValueChange={(value) => updateFilter('role', value === 'all' ? '' : value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select
                value={filters.active !== undefined ? filters.active.toString() : 'all'}
                onValueChange={(value) => updateFilter('active', value === 'all' ? undefined : value === 'true')}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Balcão */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Balcão</label>
              <Select
                value={filters.branch ? filters.branch.toString() : 'all'}
                onValueChange={(value) => updateFilter('branch', value === 'all' ? undefined : parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Balcões</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id.toString()}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Usuários */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg flex items-center gap-2">
              Usuários Encontrados
              <Badge variant="secondary">{totalUsers}</Badge>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">Mostrar</span>
              <Select
                value={filters.limit?.toString() || '10'}
                onValueChange={(value) => updateFilter('limit', parseInt(value))}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-gray-600 dark:text-gray-400">por página</span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Loading State */}
          {isLoading && (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Carregando usuários...</span>
            </div>
          )}

          {/* Tabela */}
          {!isLoading && (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Perfil</TableHead>
                    <TableHead>Balcão</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Último Acesso</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        {filters.search || filters.role || filters.branch || filters.active !== undefined
                          ? 'Nenhum usuário encontrado com os critérios de busca'
                          : 'Nenhum usuário cadastrado'}
                      </TableCell>
                    </TableRow>
                  ) : (
                    users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.full_name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{user.role_name}</Badge>
                        </TableCell>
                        <TableCell>{user.branch_name || '-'}</TableCell>
                        <TableCell>{getStatusBadge(user.is_active)}</TableCell>
                        <TableCell>{formatDate(user.last_login)}</TableCell>
                        <TableCell>
                          <ActionMenu
                            items={[
                              {
                                label: 'Visualizar',
                                icon: Eye,
                                onClick: () => handleViewUser(user)
                              },
                              {
                                label: 'Editar',
                                icon: Edit,
                                onClick: () => handleEditUser(user),
                                separator: true
                              },
                              {
                                label: user.is_active ? 'Desativar' : 'Ativar',
                                icon: user.is_active ? UserX : UserCheck,
                                onClick: () => handleToggleUserStatus(user),
                                variant: user.is_active ? 'warning' : 'default'
                              },
                              {
                                label: 'Excluir',
                                icon: Trash2,
                                onClick: () => handleDeleteUser(user),
                                variant: 'destructive'
                              }
                            ]}
                          />
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Paginação */}
          {!isLoading && totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Mostrando {((filters.page! - 1) * filters.limit!) + 1} a {Math.min(filters.page! * filters.limit!, totalUsers)} de {totalUsers} usuários
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateFilter('page', Math.max(filters.page! - 1, 1))}
                  disabled={filters.page === 1}
                >
                  Anterior
                </Button>
                <span className="text-sm">
                  Página {filters.page} de {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateFilter('page', Math.min(filters.page! + 1, totalPages))}
                  disabled={filters.page === totalPages}
                >
                  Próxima
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modais */}
      <UserViewModal
        user={viewModalUser}
        isOpen={!!viewModalUser}
        onClose={() => setViewModalUser(null)}
      />

      <UserEditModal
        user={editModalUser}
        isOpen={!!editModalUser}
        onClose={() => setEditModalUser(null)}
        onUserUpdated={loadUsers}
      />
    </div>
  );
};

export default ListarUsuario;
