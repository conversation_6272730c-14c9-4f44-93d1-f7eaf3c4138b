
import React, { useState, useEffect } from 'react';
import { Menu, LogOut, User, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import NotificationDropdown from '@/components/notifications/NotificationDropdown';
import DarkModeToggle from '@/components/ui/DarkModeToggle';
import EnhancedSearchField from '@/components/search/EnhancedSearchField';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import UserProfileModal from '@/components/auth/UserProfileModal';
import { useLogoutHandler } from '@/hooks/useLogoutHandler';

interface HeaderProps {
  onToggleSidebar: () => void;
  isMobile?: boolean;
}

const Header = ({ onToggleSidebar, isMobile = false }: HeaderProps) => {
  const [currentDateTime, setCurrentDateTime] = useState('');
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { logoutManual } = useLogoutHandler();

  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();
      // Format: DD-MM-YYYY HH:MM:SS (Angolan format with seconds)
      const day = now.getDate().toString().padStart(2, '0');
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const year = now.getFullYear();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');

      setCurrentDateTime(`${day}-${month}-${year} ${hours}:${minutes}:${seconds}`);
    };

    // Update immediately
    updateDateTime();

    // Update every second for real-time clock
    const interval = setInterval(updateDateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleLogout = () => {
    logoutManual();
  };

  const handleOpenProfile = () => {
    setIsProfileModalOpen(true);
  };

  const getUserInitials = (nome: string | undefined): string => {
    if (!nome) return 'GE';

    return nome
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .substring(0, 2)
      .toUpperCase();
  };

  const getRoleDisplayName = (perfil: string): string => {
    const roleNames: { [key: string]: string } = {
      admin: 'Administrador',
      gerente: 'Gerente',
      caixa: 'Operador de Caixa',
      tesoureiro: 'Tesoureiro'
    };
    return roleNames[perfil] || perfil;
  };

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 h-16 flex items-center justify-between px-4">
      {/* Left side */}
      <div className="flex items-center space-x-4">
        {/* Mobile Menu Button - Always visible on mobile */}
        {isMobile && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleSidebar}
            className="h-10 w-10 p-0 hover:bg-twins-accent/50"
            aria-label="Abrir menu de navegação"
          >
            <Menu className="h-5 w-5" />
          </Button>
        )}

        {/* Desktop Sidebar Toggle - Only visible on desktop */}
        {!isMobile && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleSidebar}
            className="h-8 w-8 p-0 hover:bg-twins-accent/50"
            aria-label="Alternar barra lateral"
          >
            <Menu className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Center - Enhanced Search */}
      <div className="flex-1 max-w-md mx-4">
        <EnhancedSearchField
          placeholder="Pesquisar clientes, contas, transações..."
          onSearch={(criteria) => {
            // Implementar lógica de pesquisa global aqui
            console.log('Critérios de pesquisa:', criteria);
          }}
        />
      </div>

      {/* Right side */}
      <div className="flex items-center space-x-3">
        <NotificationDropdown />
        <DarkModeToggle />
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-8 w-8 rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-primary text-white text-sm">
                  {user ? getUserInitials(user.full_name) : 'GE'}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <div className="flex items-center justify-start gap-2 p-2">
              <div className="flex flex-col space-y-1 leading-none">
                <p className="font-medium">{user?.full_name || 'Usuário'}</p>
                <p className="w-[200px] truncate text-sm text-muted-foreground">
                  {user?.email || '<EMAIL>'}
                </p>
                <p className="text-xs text-muted-foreground">
                  {user ? getRoleDisplayName(user.role) : 'Gestor'}
                </p>
              </div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleOpenProfile}>
              <User className="mr-2 h-4 w-4" />
              <span>Perfil</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => navigate('/sistema')}>
              <Settings className="mr-2 h-4 w-4" />
              <span>Configurações</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>Sair</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="hidden sm:block text-sm ">
          <div className="font-medium text-twins-primary">{user ? getRoleDisplayName(user.role) : 'Gestor'}</div>
          <div className="text-twins-secondary text-xs font-medium">{currentDateTime}</div>
        </div>
      </div>

      {/* Modal de Perfil do Usuário */}
      <UserProfileModal
        isOpen={isProfileModalOpen}
        onClose={() => setIsProfileModalOpen(false)}
      />
    </header>
  );
};

export default Header;
