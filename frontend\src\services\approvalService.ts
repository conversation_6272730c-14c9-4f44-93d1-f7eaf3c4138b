import { API_CONFIG } from '../config/api';

// Interfaces para o sistema de aprovação
export interface AccountApplication {
  id: string;
  client_id: string;
  client_name: string;
  client_type: 'individual' | 'company';
  account_type: 'corrente' | 'poupanca' | 'salario' | 'junior';
  document_number: string;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  branch_name: string;
  monthly_income?: number;
  requested_by: string;
  rejection_reason?: string;
  approved_by?: string;
  approved_at?: string;
  rejected_by?: string;
  rejected_at?: string;
  account_id?: string;
}

export interface CreateApplicationRequest {
  client_id: string;
  account_type: 'corrente' | 'poupanca' | 'salario' | 'junior';
  currency_id?: number;
  initial_deposit?: number;
  overdraft_limit?: number;
  branch_id: number;
}

export interface ApplicationsResponse {
  applications: AccountApplication[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ApplicationFilters {
  search?: string;
  status?: string;
  client_type?: string;
  account_type?: string;
  branch_id?: string;
  page?: number;
  limit?: number;
}

class ApprovalService {
  private getAuthHeaders() {
    const token = localStorage.getItem('twins-bank-token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Listar solicitações de abertura de contas
   */
  async getAccountApplications(filters: ApplicationFilters = {}): Promise<ApplicationsResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const url = `${API_CONFIG.baseURL}/api/approvals/accounts?${queryParams.toString()}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Erro ao buscar solicitações: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  }

  /**
   * Obter detalhes de uma solicitação específica
   */
  async getApplicationDetails(id: string): Promise<AccountApplication> {
    const url = `${API_CONFIG.baseURL}/api/approvals/accounts/${id}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Erro ao buscar detalhes: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.application;
  }

  /**
   * Criar solicitação de abertura de conta para cliente individual
   */
  async createIndividualApplication(applicationData: CreateApplicationRequest): Promise<{
    application_id: string;
    client_name: string;
    account_type: string;
    status: string;
  }> {
    const url = `${API_CONFIG.baseURL}/api/approvals/accounts/individual`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(applicationData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Erro ao criar solicitação: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  }

  /**
   * Criar solicitação de abertura de conta para cliente empresa
   */
  async createCompanyApplication(applicationData: CreateApplicationRequest): Promise<{
    application_id: string;
    client_name: string;
    account_type: string;
    status: string;
  }> {
    const url = `${API_CONFIG.baseURL}/api/approvals/accounts/company`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(applicationData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Erro ao criar solicitação: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  }

  /**
   * Aprovar solicitação de abertura de conta
   */
  async approveApplication(id: string): Promise<{
    application_id: string;
    account_id: string;
    account_number: string;
  }> {
    const url = `${API_CONFIG.baseURL}/api/approvals/accounts/${id}/approve`;
    
    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Erro ao aprovar solicitação: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  }

  /**
   * Rejeitar solicitação de abertura de conta
   */
  async rejectApplication(id: string, reason: string): Promise<void> {
    const url = `${API_CONFIG.baseURL}/api/approvals/accounts/${id}/reject`;
    
    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ reason })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Erro ao rejeitar solicitação: ${response.statusText}`);
    }
  }

  /**
   * Obter estatísticas das solicitações
   */
  async getApplicationStats(): Promise<{
    pending: number;
    approved: number;
    rejected: number;
    total: number;
  }> {
    try {
      const response = await this.getAccountApplications({ limit: 1000 });
      const applications = response.applications;

      return {
        pending: applications.filter(app => app.status === 'pending').length,
        approved: applications.filter(app => app.status === 'approved').length,
        rejected: applications.filter(app => app.status === 'rejected').length,
        total: applications.length
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas:', error);
      return { pending: 0, approved: 0, rejected: 0, total: 0 };
    }
  }
}

export const approvalService = new ApprovalService();
