import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import ActionMenu from '@/components/ui/ActionMenu';
import { Search, Edit, Trash2, FileText, Eye } from 'lucide-react';

interface Transferencia {
  codTransf: string;
  nrContaOrigem: string;
  natureza: string;
  valor: number;
  data: string;
  destinatario: string;
  status: 'Processada' | 'Pendente' | 'Cancelada';
}

const mockTransferencias: <PERSON><PERSON><PERSON>[] = [
  {
    codTransf: 'trans-000002',
    nrContaOrigem: '1065198662',
    natureza: '10',
    valor: 5000000.00,
    data: '2024-11-15',
    destinatario: 'Marcia Filomena Sólia Tati',
    status: 'Processada'
  },
  {
    codTransf: 'trans-000002',
    nrContaOrigem: '1013659959',
    natureza: '10',
    valor: 1250000.00,
    data: '2024-11-15',
    destinatario: 'DOMINGAS RIBEIRO',
    status: 'Processada'
  },
  {
    codTransf: 'trans-000002',
    nrContaOrigem: '1030811631',
    natureza: '10',
    valor: 1250000.00,
    data: '2024-11-15',
    destinatario: 'Joelma Ariadne Mateus Manuel',
    status: 'Processada'
  },
  {
    codTransf: 'trans-000002',
    nrContaOrigem: '1056571057',
    natureza: '10',
    valor: 293578.00,
    data: '2024-11-29',
    destinatario: 'Adélia Tatiana da silva Cruz Lima',
    status: 'Processada'
  },
  {
    codTransf: 'trans-000002',
    nrContaOrigem: '1056571057',
    natureza: '10',
    valor: 886625.00,
    data: '2024-11-29',
    destinatario: 'Neide carvalho bunga',
    status: 'Processada'
  },
  {
    codTransf: 'trans-000002',
    nrContaOrigem: '1056571057',
    natureza: '10',
    valor: 612816.00,
    data: '2024-11-29',
    destinatario: 'Jandira Teresa Martins Paulo',
    status: 'Processada'
  }
];

export default function ConsultarTransferencias() {
  const [transferencias, setTransferencias] = useState<Transferencia[]>(mockTransferencias);
  const [filtros, setFiltros] = useState({
    search: '',
    natureza: 'all',
    status: 'all',
    itemsPorPagina: '10'
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTransferencia, setSelectedTransferencia] = useState<Transferencia | null>(null);

  const formatarMoeda = (valor: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(valor);
  };

  const formatarData = (data: string) => {
    return new Date(data).toLocaleDateString('pt-AO');
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'Processada': 'default',
      'Pendente': 'secondary',
      'Cancelada': 'destructive'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status}
      </Badge>
    );
  };

  const handleVisualizarTransferencia = (transferencia: Transferencia) => {
    setSelectedTransferencia(transferencia);
    setIsModalOpen(true);
  };

  const handleEditarTransferencia = (codTransf: string) => {
    console.log('Editar transferência:', codTransf);
    // Implementar lógica de edição
  };

  const handleExcluirTransferencia = (codTransf: string) => {
    console.log('Excluir transferência:', codTransf);
    // Implementar lógica de exclusão
  };

  const handleGerarRelatorio = (codTransf: string) => {
    console.log('Gerar relatório:', codTransf);
    // Implementar lógica de relatório
  };

  const transferenciasFiltradas = transferencias.filter(transferencia => {
    const matchSearch = !filtros.search ||
      transferencia.codTransf.toLowerCase().includes(filtros.search.toLowerCase()) ||
      transferencia.nrContaOrigem.includes(filtros.search) ||
      transferencia.destinatario.toLowerCase().includes(filtros.search.toLowerCase());

    const matchNatureza = filtros.natureza === 'all' || transferencia.natureza === filtros.natureza;
    const matchStatus = filtros.status === 'all' || transferencia.status === filtros.status;

    return matchSearch && matchNatureza && matchStatus;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight dark:text-gray-100">Consultar Transferências</h1>
          <p className="text-muted-foreground dark:text-gray-400">
            Consulte e gerencie todas as transferências realizadas
          </p>
        </div>
        <Badge variant="destructive" className="text-sm">
          406
        </Badge>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros de Pesquisa</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Pesquisar</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Código, conta ou destinatário..."
                  value={filtros.search}
                  onChange={(e) => setFiltros(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Natureza</label>
              <Select value={filtros.natureza} onValueChange={(value) => setFiltros(prev => ({ ...prev, natureza: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Todas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  <SelectItem value="10">10 - Transferência Interna</SelectItem>
                  <SelectItem value="20">20 - Transferência Externa</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={filtros.status} onValueChange={(value) => setFiltros(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="Processada">Processada</SelectItem>
                  <SelectItem value="Pendente">Pendente</SelectItem>
                  <SelectItem value="Cancelada">Cancelada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Mostrar</label>
              <Select value={filtros.itemsPorPagina} onValueChange={(value) => setFiltros(prev => ({ ...prev, itemsPorPagina: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Transferências */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Transferências Encontradas ({transferenciasFiltradas.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cód Transf</TableHead>
                  <TableHead>Nº Conta Origem</TableHead>
                  <TableHead>Natureza</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead>Destinatário</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Ação</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transferenciasFiltradas.slice(0, parseInt(filtros.itemsPorPagina)).map((transferencia, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{transferencia.codTransf}</TableCell>
                    <TableCell>{transferencia.nrContaOrigem}</TableCell>
                    <TableCell>{transferencia.natureza}</TableCell>
                    <TableCell className="font-medium text-green-600">
                      {formatarMoeda(transferencia.valor)}
                    </TableCell>
                    <TableCell>{formatarData(transferencia.data)}</TableCell>
                    <TableCell>{transferencia.destinatario}</TableCell>
                    <TableCell>{getStatusBadge(transferencia.status)}</TableCell>
                    <TableCell>
                      <ActionMenu
                        items={[
                          {
                            label: 'Visualizar',
                            icon: Eye,
                            onClick: () => handleVisualizarTransferencia(transferencia)
                          },
                          {
                            label: 'Gerar Relatório',
                            icon: FileText,
                            onClick: () => handleGerarRelatorio(transferencia.codTransf),
                            separator: true
                          },
                          {
                            label: 'Editar',
                            icon: Edit,
                            onClick: () => handleEditarTransferencia(transferencia.codTransf),
                            disabled: transferencia.status === 'Processada'
                          },
                          {
                            label: 'Excluir',
                            icon: Trash2,
                            onClick: () => handleExcluirTransferencia(transferencia.codTransf),
                            variant: 'destructive',
                            disabled: transferencia.status === 'Processada'
                          }
                        ]}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modal de Detalhes da Transferência */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-md dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="dark:text-gray-100">Detalhes da Transferência</DialogTitle>
          </DialogHeader>
          {selectedTransferencia && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Código</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedTransferencia.codTransf}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Status</label>
                  <p className="text-sm">{getStatusBadge(selectedTransferencia.status)}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Conta Origem</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedTransferencia.nrContaOrigem}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Natureza</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{selectedTransferencia.natureza}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium dark:text-gray-100">Destinatário</label>
                <p className="text-sm text-gray-600 dark:text-gray-400">{selectedTransferencia.destinatario}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Valor</label>
                  <p className="text-sm font-bold text-green-600">{formatarMoeda(selectedTransferencia.valor)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium dark:text-gray-100">Data</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{formatarData(selectedTransferencia.data)}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
