import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  User,
  Building2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  FileText,
  DollarSign,
  Globe,
  Briefcase,
  Heart,
  CreditCard
} from 'lucide-react';
import { Client } from '@/types/client';

interface ClientViewModalProps {
  client: Client | null;
  isOpen: boolean;
  onClose: () => void;
}

const ClientViewModal: React.FC<ClientViewModalProps> = ({
  client,
  isOpen,
  onClose,
}) => {
  if (!client) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'default',
      inactive: 'secondary',
      blocked: 'destructive'
    } as const;

    const labels = {
      active: 'Ativo',
      inactive: 'Inativo',
      blocked: 'Bloqueado'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {client.client_type === 'individual' ? (
              <User className="h-6 w-6 text-blue-600" />
            ) : (
              <Building2 className="h-6 w-6 text-purple-600" />
            )}
            <span>{client.full_name || client.company_name}</span>
            {getStatusBadge(client.status)}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Informações Básicas */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Informações Básicas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Tipo de Cliente</label>
                  <p className="text-sm">
                    {client.client_type === 'individual' ? 'Pessoa Física' : 'Pessoa Jurídica'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Documento</label>
                  <p className="text-sm">{client.document_type} {client.document_number}</p>
                </div>
              </div>

              {client.nif && (
                <div>
                  <label className="text-sm font-medium text-gray-600">NIF</label>
                  <p className="text-sm">{client.nif}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Nacionalidade</label>
                  <p className="text-sm flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    {client.nationality}
                  </p>
                </div>
                {client.gender && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">Gênero</label>
                    <p className="text-sm">{client.gender === 'M' ? 'Masculino' : 'Feminino'}</p>
                  </div>
                )}
              </div>

              {client.birth_date && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Data de Nascimento</label>
                  <p className="text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(client.birth_date)}
                  </p>
                </div>
              )}

              {client.incorporation_date && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Data de Constituição</label>
                  <p className="text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(client.incorporation_date)}
                  </p>
                </div>
              )}

              {client.marital_status && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Estado Civil</label>
                  <p className="text-sm flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    {client.marital_status}
                  </p>
                </div>
              )}

              {client.profession && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Profissão</label>
                  <p className="text-sm flex items-center gap-2">
                    <Briefcase className="h-4 w-4" />
                    {client.profession}
                  </p>
                </div>
              )}

              {client.monthly_income && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Rendimento Mensal</label>
                  <p className="text-sm flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    {formatCurrency(client.monthly_income)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contactos */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Contactos
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {client.contacts && client.contacts.length > 0 ? (
                client.contacts.map((contact, index) => (
                  <div key={index} className="flex items-center gap-3">
                    {contact.contact_type.includes('phone') ? (
                      <Phone className="h-4 w-4 text-green-600" />
                    ) : (
                      <Mail className="h-4 w-4 text-blue-600" />
                    )}
                    <div>
                      <p className="text-sm font-medium">
                        {contact.contact_type.replace('_', ' ').toUpperCase()}
                        {contact.is_primary && <Badge variant="outline" className="ml-2">Principal</Badge>}
                      </p>
                      <p className="text-sm text-gray-600">{contact.contact_value}</p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">Nenhum contacto registrado</p>
              )}
            </CardContent>
          </Card>

          {/* Endereços */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Endereços
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {client.addresses && client.addresses.length > 0 ? (
                client.addresses.map((address, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-red-600" />
                      <span className="text-sm font-medium">
                        {address.address_type.toUpperCase()}
                        {address.is_primary && <Badge variant="outline" className="ml-2">Principal</Badge>}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 ml-6">
                      {address.street}
                      {address.house_number && `, ${address.house_number}`}
                      <br />
                      {address.neighborhood && `${address.neighborhood}, `}
                      {address.municipality}, {address.province}
                      {address.postal_code && ` - ${address.postal_code}`}
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">Nenhum endereço registrado</p>
              )}
            </CardContent>
          </Card>

          {/* Informações do Sistema */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Informações do Sistema
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Balcão</label>
                <p className="text-sm">{client.branch_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Criado por</label>
                <p className="text-sm">{client.created_by_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Data de Criação</label>
                <p className="text-sm">{formatDate(client.created_at)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Última Atualização</label>
                <p className="text-sm">{formatDate(client.updated_at)}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ClientViewModal;
